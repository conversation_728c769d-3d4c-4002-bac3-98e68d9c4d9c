[![Version](https://img.shields.io/badge/version-1.6.2-blue.svg)](https://github.com/xxphantom/remnawave-installer)
[![Language](https://img.shields.io/badge/language-Bash-green.svg)]()
[![OS Support](https://img.shields.io/badge/OS-Ubuntu-orange.svg)]()

[🇷🇺 Читать на русском](README.ru.md)

This Bash script is designed for **automated installation and initial configuration of Remnawave components** (Panel, Node, Subscription-Page) and their environment (Docker, Caddy, UFW). The script simplifies deployment, helps to study the result and learn how to deploy the panel manually.

> [!CAUTION] 
> **WARNING!** This script and repository are provided as an **educational example** for studying the interaction between Caddy and Remnawave panel, as well as for demonstrating Caddy configuration as a reverse proxy. **The script is not intended for production use without full understanding of its actions and Remnawave configurations.** If you don't understand how the Remnawave management panel works or the configurations created by the script, it's your responsibility. **USE AT YOUR OWN RISK!**

---

## 🔶 Table of Contents

- [What does this script do?](#-what-does-this-script-do)
- [Key features of the script](#-key-features-of-the-script)
- [Requirements for script operation and Remnawave installation](#-requirements-for-script-operation-and-remnawave-installation)
  - [Hardware (for Remnawave)](#hardware-for-remnawave)
  - [Software (for server and script)](#software-for-server-and-script)
  - [Network](#network)
- [Quick start: Running the script](#-quick-start-running-the-script)
- [Installation scenarios supported by the script](#-installation-scenarios-supported-by-the-script)
  - [Option 1: Two servers (Panel and Node separately)](#option-1-two-servers-panel-and-node-separately)
  - [Option 2: All-in-One (Panel and Node on one server)](#option-2-all-in-one-panel-and-node-on-one-server)
- [Panel access protection options configured by the script](#️-panel-access-protection-options-configured-by-the-script)
  - [SIMPLE cookie security](#simple-cookie-security)
  - [FULL Caddy security (recommended)](#full-caddy-security-recommended)
- [Using the script: Menu and options](#️-using-the-script-menu-and-options)
  - [Main menu](#main-menu)
  - [Installation menu](#installation-menu)
  - [Domains](#domains)
- [Important information generated by the script](#-important-information-generated-by-the-script)
- [Directory structure created by the script](#-directory-structure-created-by-the-script)
- [Managing services after installation](#️-managing-services-after-installation)
- [Update System](#-update-system)
- [WARP Integration](#-warp-integration)
- [Official Remnawave resources](#-official-remnawave-resources)
- [Notes and known issues](#️-notes-and-known-issues)

---

## 🎯 What does this script do?

This script **is not a replacement for the Remnawave panel**. It **automates the deployment process** of the Remnawave panel itself, its nodes, and necessary accompanying services.

**Script responsibilities:**

- Preparing the server environment (installing Docker, UFW, dependencies).
- Downloading necessary Docker images for Remnawave Panel, Node, Subscription Page, Caddy, PostgreSQL, Redis.
- Generating configuration files (`docker-compose.yml`, `.env` for Remnawave, `Caddyfile`).
- Configuring Caddy reverse proxy for access to the panel web interface and other services.
- Implementing two access protection options for the panel via Caddy.
- Initial Remnawave setup: creating administrator, registering node, creating test user and VLESS configurations.
- Providing a convenient menu for installation management (reinstall, removal, restart).
- Saving all generated credentials and URLs to a local file.

**Remnawave panel responsibilities (after installation by script):**

- Managing users, subscriptions, traffic.
- Managing nodes and their configurations.
- Providing API for clients and nodes.
- Statistics and monitoring.
- All other functions inherent to a VPN/proxy management panel.

---

## ✨ Key features of the script

- **Interactive menu**: User-friendly interface for choosing options in Russian or English.
- **Automatic dependency installation**: Docker, Docker Compose plugin, UFW, `curl`, `jq`, `openssl`, etc.
- **Different installation modes**:
  - Panel Only Remnawave (with two Caddy protection options).
  - Node Only Remnawave (for installation on a separate server).
  - All-in-One: Panel + Node Remnawave on one server (with two Caddy protection options).
- **Access security configuration via Caddy**:
  - **SIMPLE cookie**: Panel access through a secret key in the URL.
  - **FULL Caddy security**: Using full Caddy authentication portal (via `remnawave/caddy-with-auth`).
- **Automatic configuration generation**:
  - `.env` for Remnawave Panel and Node.
  - `docker-compose.yml` for all components.
  - `Caddyfile` for reverse proxy, SSL, and selected security type.
  - `Makefile` for simple Docker container management.
- **Initial Remnawave initialization**: Admin, node, and user registration.
- **Installation management**: Restart, complete removal (with data), access to Remnawave Rescue CLI.
- **Update system**: Comprehensive update functionality for Remnawave components with smart Docker image detection.
- **WARP integration**: Easy setup of Cloudflare WARP with domain customization options.
- **Display and save credentials**: All important data is saved to `/opt/remnawave/credentials.txt`.
- **BBR management**: Enable/disable BBR.
- **QR code generation**: For subscription URL when installing in All-in-One scenario (Panel + Node).

---

## 📋 Requirements for script operation and Remnawave installation

### Hardware (for Remnawave)

The script installs Remnawave, so these requirements apply to Remnawave components.

- **Remnawave Panel**:
  - OS: Ubuntu 22.04 recommended (script was tested on this Ubuntu version)
  - RAM: Minimum 2GB, 4GB recommended.
  - CPU: Minimum 2 cores, 4 cores recommended.
  - Storage: Minimum 20GB.
- **Remnawave Node**:
  - OS: Ubuntu or Debian recommended.
  - RAM: Minimum 1GB.
  - CPU: Minimum 1 core.

### Software (for server and script)

- **OS**: Ubuntu (22.04 recommended) or Debian.
- **Root access**: Script must be run with `root` privileges (`sudo`).
- **Basic utilities**: Docker, Docker Compose plugin, UFW, `curl`, `jq`, `openssl`, etc. The script will attempt to install them.

### Network

- **Domains**:
  - **Panel Domain**: (e.g., `panel.example.com`) - for accessing the Remnawave web interface.
  - **Subscription Domain**: (e.g., `sub.example.com`) - for public subscription page and API.
  - **Selfsteal Domain**: (e.g., `login.example.com`) - for VLESS REALITY.
  - All three domains must be **unique**.
  - **Important!** DNS records for these domains must be correctly configured and point to your server IP addresses **BEFORE** running the script.
- **Open ports** (script will attempt to configure UFW):
  - `80/tcp`, `443/tcp` (for Caddy).
  - Your SSH port (script will determine it or use `22/tcp`).
  - For panel with Node: port `2222/tcp` will be open only for **********/16 (Docker subnet).
  - For separate Node installation: port `2222/tcp` on the Node server will be open only for the panel IP.

---

## 🚀 Quick start: Running the script

1.  **Download and run the script with one command as root**:

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=en
```

2.  **Follow the on-screen instructions**: The script will guide you through all installation and configuration steps.

### Advanced usage with version control

The script supports additional parameters to control which versions of components to use:

**Use development version of Remnawave:**

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=en --panel-branch=dev
```

**Use development script and Remnawave version:**

```bash
sudo bash -c "$(curl -sL https://raw.githubusercontent.com/xxphantom/remnawave-installer/refs/heads/main/install.sh)" @ --lang=en --panel-branch=dev --installer-branch=dev
```

**Available parameters:**

- `--lang=en|ru` - Interface language
- `--panel-branch=main|dev` - Remnawave backend and node Docker image versions
- `--installer-branch=main|dev` - Installer script branch (affects download URL)

---

## 🔩 Installation scenarios supported by the script

The script allows you to deploy Remnawave in two main configurations:

### Option 1: Two servers (Panel and Node separately)

Recommended for better reliability and flexibility.

- **Panel Server**: The script installs Remnawave Panel, Subscription-Page, Caddy (for panel and subscriptions), PostgreSQL, Redis. Remnawave Node with Xray is not placed here.
- **Node Server**: The script installs Remnawave Node (with Xray) and Caddy for serving the Selfsteal domain.
- **DNS**:
  - Panel and Subscription domains → Panel server IP.
  - Selfsteal domain → Node server IP.
- **Order of actions with the script**:
  1.  On the Panel server, select "Panel Only" installation. The script will output the public key (`SSL_CERT="..."`), which needs to be saved.
  2.  On the Node server, select "Node only" installation. The script will request the selfsteal domain, panel IP, and the previously saved public key.

### Option 2: All-in-One (Panel and Node on one server)

Simplified option, suitable for testing or small loads.

- **One server**: The script installs Remnawave Panel, Node, Subscription-Page, Caddy, PostgreSQL, Redis.
- **DNS**: All three domains (different!) (Panel, Subscription, Selfsteal) → IP of this single server.
- **Traffic routing (as configured by the script)**:
  Remnawave Node (Xray) listens on port `443` and forwards traffic that is not a VLESS connection to the local Caddy port (e.g., `9443`).
  Caddy listens on a locally accessible port (e.g., `9443`) and forwards traffic based on SNI:

  - Requests with SNI for the panel domain are forwarded to port `3000` (Remnawave Panel).
  - Requests with SNI for the subscription domain are forwarded to port `3010` (Subscription Page).
  - Requests with SNI for the Selfsteal domain are forwarded to a static HTML page (Selfsteal).

  ```
  Client → 443 port → Xray (local Remnawave Node)
                        ├─ (VLESS proxy traffic) → Processed by Xray
                        └─ (Non-VLESS traffic, fallback) → Caddy (listening on internal port, e.g., 9443)
                                                          ├─ SNI for Panel Domain → Remnawave Panel (port 3000)
                                                          ├─ SNI for Subscription Domain → Subscription Page (port 3010)
                                                          └─ SNI for Selfsteal Domain → Static HTML page
  ```

  > **Important**: In this mode, if you stop the local Remnawave Node or mess up the Xray config, the panel and other web services will become inaccessible through domain names, as Caddy will not receive traffic. Currently, the script does not provide an option to "Open direct access to the panel", but it will be added soon. For now, this is solved by manually editing the Caddyfile (replace 127.0.0.1 with 0.0.0.0)

---

## 🛡️ Panel access protection options configured by the script

The script offers two access protection options for the Remnawave Panel web interface using Caddy:

### SIMPLE cookie security

- Panel access is done through a special URL containing a secret key (e.g., `https://panel.example.com/auth/login?caddy=YOUR_SECRET_KEY`).
- On first visit to such a link, Caddy sets a cookie. Subsequent requests are authorized by this cookie.
- If the cookie/parameter is missing or incorrect, Caddy shows a static placeholder page (Selfsteal site).
- The script generates the secret key and URL, saving them to `credentials.txt`.

### FULL Caddy security (recommended)

- Uses a special image `remnawave/caddy-with-auth`, including the `caddy-security` module for a full authentication portal.
- **Two-level authentication**:
  1.  **Caddy Auth Portal**: User first logs in here (login/password generated by the script, MFA configured on first login).
  2.  **Remnawave Panel**: After success at the first level, the user gets access to the standard Remnawave login page (own login/password, generated by the script).
- The panel is accessible via a unique, randomly generated path (e.g., `https://panel.example.com/<RANDOM_PATH>/auth`).
- The script generates all necessary credentials and URLs, saving them to `credentials.txt`.

---

## 🛠️ Using the script: Menu and options

After launch, the script will display the main menu. Language selection is done via parameter at launch (see [Quick start](#-quick-start-running-the-script)).

![Main Menu](assets/menu.png)

---

### Domains

The script will request three domain names from you:

- **Panel domain** (used for `FRONT_END_DOMAIN` in panel `.env`).
- **Subscription domain** (used for `SUB_PUBLIC_DOMAIN` in panel `.env`).
- **Selfsteal domain**.

Make sure DNS A-records for these domains point to the correct IP addresses **before** starting the installation.

## 🔑 Important information generated by the script

After successful installation, the script saves all critical information (access URLs, logins, passwords, secret keys) to a file:

- `/opt/remnawave/credentials.txt` (for installations including the panel).

**Securely save this file!**

---

## 📂 Directory structure created by the script

The script organizes configuration files as follows:

- `/opt/remnawave/`: Main directory for Remnawave panel.
  - `.env`, `docker-compose.yml`, `credentials.txt`, `config.json` (temporary for Xray).
  - `caddy/`: Caddy configuration for panel (`Caddyfile`, `docker-compose.yml`, `html/`).
  - `subscription-page/`: Subscription page configuration (`docker-compose.yml`).
  - `node/`: (Only for All-in-One) Local node configuration (`.env`, `docker-compose.yml`).
- `/opt/remnanode/`: Directory for separate Remnawave Node installation.
  - `.env`, `docker-compose.yml`.
  - `selfsteal/`: Caddy configuration for Selfsteal on node (`Caddyfile`, `docker-compose.yml`, `html/`).

---

## ⚙️ Managing services after installation

In each directory where the script creates `docker-compose.yml` (`/opt/remnawave`, `/opt/remnawave/caddy`, `/opt/remnanode`, etc.), a `Makefile` is also created. You can use it to manage Docker services:

```bash
cd /opt/remnawave # or other appropriate directory
make start    # Start services and show logs
make stop     # Stop services
make restart  # Restart services
make logs     # Show logs of running services
```

---

## 🔄 Update System

Smart update system for Remnawave components with Docker image detection. Only restarts services with actual updates, includes automatic cleanup of unused images.

---

## 🌐 WARP Integration

Easy integration with Cloudflare WARP for routing traffic through Cloudflare's network. Automated setup. No separate node configuration required - Xray config is transmitted to nodes and Xray core handles Cloudflare connections.

---

## 🔗 Official Remnawave resources

This script automates many steps, but by itself doesn't provide understanding of what's happening. Therefore, I recommend performing a manual installation after getting familiar with the panel, and for this you'll find the official Remnawave resources useful:

- 📖 [**Documentation**](https://remna.st) — official website
- 📰 [**@remnalog**](https://t.me/remnalog) — latest updates
- 📢 [**Channel**](https://t.me/remnawave) — official announcements and links
- 💬 [**Group**](https://t.me/+xQs17zMzwCY1NzYy) — chat for discussion

## ⚠️ Notes and known issues

DNS: It's extremely important to correctly configure DNS A-records for all domains before running the script.

Cloudflare: For the Selfsteal domain, don't use Cloudflare proxying (orange cloud). The DNS record should be "DNS Only" (gray cloud). The script will try to check this, but you shouldn't fail either.

Docker Hub Rate Limits: With frequent installations and with some hosting providers, you may encounter image download limits. The script will notify you about this. The easiest solution is to run docker login and authenticate.

Clean installation: The script is recommended to run on a "clean" server without third-party software (Nginx, Apache), especially services occupying ports 80/443, etc.

Firewall: The script configures UFW. When using another firewall system, manual configuration may be required.

---

For all questions related to the script, please contact [**@xxphantom**](https://t.me/uphantom).
